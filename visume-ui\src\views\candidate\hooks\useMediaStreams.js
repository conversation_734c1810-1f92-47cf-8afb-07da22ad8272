import { useState, useCallback } from "react";
import toast from "react-hot-toast";
import { MediaStreamVerification } from "../../utils/mediaStreamVerification";

export function useMediaStreams() {
  const [localCamStream, setLocalCamStream] = useState(null);


  const startWebcam = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
        },
      });

      // Check if the stream contains both video and audio tracks
      const videoTrack = stream.getVideoTracks()[0];
      const audioTrack = stream.getAudioTracks()[0];

      if (!videoTrack || !audioTrack) {
        stream.getTracks().forEach((track) => track.stop());
        return toast.error("Both webcam and microphone must be shared to start the interview.");
      }
      setLocalCamStream(stream);

      // Register stream for tracking
      MediaStreamVerification.registerStream(stream, 'useMediaStreams-main');
    } catch (err) {
      console.error("Error starting webcam and audio: ", err);
      toast.error("There was an error accessing your camera or microphone.");

    }
  }, []);

  const stopAllStreams = useCallback(() => {
    console.log("stopAllStreams called - performing comprehensive media cleanup");

    try {
      if (localCamStream) {
        const tracks = localCamStream.getTracks();
        console.log(`Stopping ${tracks.length} media tracks from main camera stream`);

        tracks.forEach((track, index) => {
          try {
            console.log(`Stopping ${track.kind} track ${index + 1}/${tracks.length} (${track.label})`);
            track.stop();
            console.log(`✓ Successfully stopped ${track.kind} track`);
          } catch (trackError) {
            console.error(`✗ Error stopping ${track.kind} track:`, trackError);
          }
        });

        // Unregister stream from tracking
        MediaStreamVerification.unregisterStream(localCamStream, 'useMediaStreams-main');

        setLocalCamStream(null);
        console.log("✓ Main camera stream cleaned up successfully");

        // Additional cleanup to ensure browser releases resources
        setTimeout(() => {
          console.log("✓ Delayed cleanup verification completed");
        }, 100);

      } else {
        console.log("No main camera stream to stop");
      }

      // Force garbage collection if available (development/debugging)
      if (window.gc && typeof window.gc === 'function') {
        try {
          window.gc();
          console.log("✓ Forced garbage collection");
        } catch (gcError) {
          console.log("Garbage collection not available");
        }
      }

    } catch (error) {
      console.error("✗ Error in stopAllStreams:", error);
    }
  }, [localCamStream]);

  return {
    localCamStream,
    startWebcam,
    stopAllStreams,
  };
}
