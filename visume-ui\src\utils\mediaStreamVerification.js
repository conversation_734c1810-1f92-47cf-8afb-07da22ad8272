/**
 * Media Stream Verification Utility
 * This utility helps verify that media streams are properly cleaned up
 */

export const MediaStreamVerification = {
  // Track active streams for debugging
  activeStreams: new Set(),
  
  // Register a stream for tracking
  registerStream: (stream, source = 'unknown') => {
    if (stream && stream.getTracks) {
      MediaStreamVerification.activeStreams.add({
        stream,
        source,
        timestamp: new Date().toISOString(),
        tracks: stream.getTracks().map(track => ({
          kind: track.kind,
          label: track.label,
          readyState: track.readyState
        }))
      });
      console.log(`📹 Registered media stream from ${source}:`, {
        trackCount: stream.getTracks().length,
        tracks: stream.getTracks().map(t => `${t.kind}:${t.label}`)
      });
    }
  },
  
  // Unregister a stream
  unregisterStream: (stream, source = 'unknown') => {
    const streamEntry = Array.from(MediaStreamVerification.activeStreams)
      .find(entry => entry.stream === stream);
    
    if (streamEntry) {
      MediaStreamVerification.activeStreams.delete(streamEntry);
      console.log(`🛑 Unregistered media stream from ${source}`);
    }
  },
  
  // Check for active streams
  checkActiveStreams: () => {
    const activeCount = MediaStreamVerification.activeStreams.size;
    console.log(`🔍 Active media streams: ${activeCount}`);
    
    if (activeCount > 0) {
      console.warn('⚠️ Found active media streams that may need cleanup:');
      MediaStreamVerification.activeStreams.forEach((entry, index) => {
        console.warn(`  ${index + 1}. Source: ${entry.source}, Tracks: ${entry.tracks.length}`);
        entry.tracks.forEach(track => {
          console.warn(`     - ${track.kind}: ${track.label} (${track.readyState})`);
        });
      });
    } else {
      console.log('✅ No active media streams detected');
    }
    
    return activeCount;
  },
  
  // Force cleanup of all tracked streams
  forceCleanupAll: () => {
    console.log('🧹 Force cleaning up all tracked media streams...');
    let cleanedCount = 0;
    
    MediaStreamVerification.activeStreams.forEach(entry => {
      try {
        if (entry.stream && entry.stream.getTracks) {
          entry.stream.getTracks().forEach(track => {
            if (track.readyState === 'live') {
              track.stop();
              cleanedCount++;
            }
          });
        }
      } catch (error) {
        console.error(`Error cleaning up stream from ${entry.source}:`, error);
      }
    });
    
    MediaStreamVerification.activeStreams.clear();
    console.log(`✅ Force cleanup completed. Stopped ${cleanedCount} tracks.`);
    return cleanedCount;
  },
  
  // Monitor browser media indicators
  monitorBrowserIndicators: () => {
    // This is a helper function to remind developers to check browser indicators
    console.log('👀 Please check browser tab for camera/microphone indicators:');
    console.log('   - Chrome: Look for camera/microphone icons in the tab');
    console.log('   - Firefox: Look for camera/microphone icons in the address bar');
    console.log('   - Safari: Look for camera/microphone icons in the tab');
    console.log('   - Edge: Look for camera/microphone icons in the tab');
    
    // Try to detect if getUserMedia is still active (limited detection)
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      navigator.mediaDevices.enumerateDevices()
        .then(devices => {
          const videoDevices = devices.filter(d => d.kind === 'videoinput');
          const audioDevices = devices.filter(d => d.kind === 'audioinput');
          console.log(`📱 Available devices: ${videoDevices.length} video, ${audioDevices.length} audio`);
        })
        .catch(err => console.warn('Could not enumerate devices:', err));
    }
  },
  
  // Create a verification report
  createReport: () => {
    const report = {
      timestamp: new Date().toISOString(),
      activeStreamsCount: MediaStreamVerification.activeStreams.size,
      activeStreams: Array.from(MediaStreamVerification.activeStreams).map(entry => ({
        source: entry.source,
        trackCount: entry.tracks.length,
        tracks: entry.tracks,
        registeredAt: entry.timestamp
      })),
      browserInfo: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        mediaDevicesSupported: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
      }
    };
    
    console.log('📊 Media Stream Verification Report:', report);
    return report;
  }
};

// Global helper for easy access in browser console
if (typeof window !== 'undefined') {
  window.MediaStreamVerification = MediaStreamVerification;
  console.log('🔧 MediaStreamVerification utility available globally');
  console.log('   Usage: MediaStreamVerification.checkActiveStreams()');
  console.log('   Usage: MediaStreamVerification.monitorBrowserIndicators()');
  console.log('   Usage: MediaStreamVerification.createReport()');
}

export default MediaStreamVerification;
