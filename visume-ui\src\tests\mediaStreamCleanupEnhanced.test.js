/**
 * Enhanced Media Stream Cleanup Test
 * Tests the comprehensive media stream cleanup implementation
 */

// Mock MediaStream and MediaStreamTrack
const createMockTrack = (kind, label = `${kind}-track`) => ({
  kind,
  label,
  stop: jest.fn(),
  readyState: 'live',
  enabled: true
});

const createMockStream = (tracks = []) => ({
  getTracks: jest.fn(() => tracks),
  getVideoTracks: jest.fn(() => tracks.filter(t => t.kind === 'video')),
  getAudioTracks: jest.fn(() => tracks.filter(t => t.kind === 'audio')),
  active: true
});

// Mock MediaRecorder
const createMockMediaRecorder = () => ({
  state: 'inactive',
  start: jest.fn(),
  stop: jest.fn(),
  pause: jest.fn(),
  resume: jest.fn(),
  ondataavailable: null,
  onstart: null,
  onstop: null,
  onerror: null
});

describe('Enhanced Media Stream Cleanup', () => {
  let mockVideoTrack, mockAudioTrack, mockStream, mockMediaRecorder;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create fresh mocks for each test
    mockVideoTrack = createMockTrack('video', 'Camera');
    mockAudioTrack = createMockTrack('audio', 'Microphone');
    mockStream = createMockStream([mockVideoTrack, mockAudioTrack]);
    mockMediaRecorder = createMockMediaRecorder();

    // Mock console methods
    global.console = {
      ...console,
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    };

    // Mock setTimeout
    global.setTimeout = jest.fn((fn, delay) => {
      fn();
      return 123;
    });
  });

  test('should stop all media tracks with enhanced error handling', () => {
    const React = require('react');
    
    const TestComponent = () => {
      const [localCamStream, setLocalCamStream] = React.useState(mockStream);

      const stopAllStreams = React.useCallback(() => {
        console.log("stopAllStreams called - performing comprehensive media cleanup");

        try {
          if (localCamStream) {
            const tracks = localCamStream.getTracks();
            console.log(`Stopping ${tracks.length} media tracks from main camera stream`);

            tracks.forEach((track, index) => {
              try {
                console.log(`Stopping ${track.kind} track ${index + 1}/${tracks.length} (${track.label})`);
                track.stop();
                console.log(`✓ Successfully stopped ${track.kind} track`);
              } catch (trackError) {
                console.error(`✗ Error stopping ${track.kind} track:`, trackError);
              }
            });

            setLocalCamStream(null);
            console.log("✓ Main camera stream cleaned up successfully");
          }
        } catch (error) {
          console.error("✗ Error in stopAllStreams:", error);
        }
      }, [localCamStream]);

      React.useEffect(() => {
        return () => {
          console.log("Component unmounting - performing comprehensive cleanup");
          stopAllStreams();
        };
      }, [stopAllStreams]);

      return <div data-testid="test-component">Test</div>;
    };

    const { render, unmount } = require('@testing-library/react');
    const { unmount: unmountComponent } = render(<TestComponent />);

    // Verify initial state
    expect(mockStream.getTracks).toHaveBeenCalled();
    
    // Unmount component to trigger cleanup
    unmountComponent();

    // Verify all tracks were stopped
    expect(mockVideoTrack.stop).toHaveBeenCalled();
    expect(mockAudioTrack.stop).toHaveBeenCalled();
    
    // Verify logging
    expect(console.log).toHaveBeenCalledWith("Component unmounting - performing comprehensive cleanup");
    expect(console.log).toHaveBeenCalledWith("✓ Successfully stopped video track");
    expect(console.log).toHaveBeenCalledWith("✓ Successfully stopped audio track");
  });

  test('should handle MediaRecorder cleanup with timeout', async () => {
    const React = require('react');
    
    const TestRecordingComponent = () => {
      const [mediaRecorder, setMediaRecorder] = React.useState(mockMediaRecorder);
      const [isRecording, setIsRecording] = React.useState(true);

      const stopRecording = React.useCallback(async () => {
        if (!mediaRecorder || !isRecording) {
          console.log("stopRecording: No active recording to stop");
          return true;
        }
        
        console.log("stopRecording: Stopping MediaRecorder...");
        try {
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              console.warn("stopRecording: Timeout waiting for MediaRecorder to stop");
              reject(new Error("MediaRecorder stop timeout"));
            }, 5000);
            
            mediaRecorder.onstop = () => {
              clearTimeout(timeout);
              console.log("✓ Recording stopped successfully");
              resolve();
            };
            
            mediaRecorder.stop();
            // Simulate immediate stop for test
            if (mediaRecorder.onstop) {
              mediaRecorder.onstop();
            }
          });
          
          return true;
        } catch (error) {
          console.error("✗ Failed to stop recording:", error);
          setIsRecording(false);
          return false;
        }
      }, [mediaRecorder, isRecording]);

      return (
        <div data-testid="recording-component">
          <button onClick={stopRecording} data-testid="stop-recording">
            Stop Recording
          </button>
        </div>
      );
    };

    const { render } = require('@testing-library/react');
    const { fireEvent } = require('@testing-library/react');
    const { getByTestId } = render(<TestRecordingComponent />);

    // Click stop recording
    fireEvent.click(getByTestId('stop-recording'));

    // Wait for async operation
    await new Promise(resolve => setTimeout(resolve, 0));

    // Verify MediaRecorder.stop was called
    expect(mockMediaRecorder.stop).toHaveBeenCalled();
    expect(console.log).toHaveBeenCalledWith("✓ Recording stopped successfully");
  });

  test('should perform comprehensive cleanup on component unmount', () => {
    const React = require('react');
    
    const DeviceTestMockComponent = () => {
      const [localCamStream] = React.useState(mockStream);
      
      const stopAllStreams = React.useCallback(() => {
        if (localCamStream) {
          const tracks = localCamStream.getTracks();
          tracks.forEach(track => track.stop());
        }
      }, [localCamStream]);

      const cleanup = React.useCallback(() => {
        console.log("useQuestions cleanup called");
      }, []);

      // Component unmount cleanup - matches our implementation
      React.useEffect(() => {
        return () => {
          console.log("DeviceTest component unmounting - performing comprehensive cleanup");
          
          try {
            if (typeof stopAllStreams === "function") {
              stopAllStreams();
              console.log("Stopped all media streams on unmount");
            }
            
            if (window.speechSynthesis) {
              window.speechSynthesis.cancel();
              console.log("Cancelled speech synthesis on unmount");
            }
            
            if (typeof cleanup === "function") {
              cleanup();
              console.log("Executed useQuestions cleanup on unmount");
            }
            
          } catch (error) {
            console.error("Error during component unmount cleanup:", error);
          }
        };
      }, [stopAllStreams, cleanup]);

      return <div data-testid="device-test-mock">Device Test Mock</div>;
    };

    // Mock speechSynthesis
    global.window.speechSynthesis = {
      cancel: jest.fn()
    };

    const { render } = require('@testing-library/react');
    const { unmount } = render(<DeviceTestMockComponent />);

    // Unmount to trigger cleanup
    unmount();

    // Verify comprehensive cleanup
    expect(mockVideoTrack.stop).toHaveBeenCalled();
    expect(mockAudioTrack.stop).toHaveBeenCalled();
    expect(window.speechSynthesis.cancel).toHaveBeenCalled();
    expect(console.log).toHaveBeenCalledWith("DeviceTest component unmounting - performing comprehensive cleanup");
    expect(console.log).toHaveBeenCalledWith("Stopped all media streams on unmount");
    expect(console.log).toHaveBeenCalledWith("Cancelled speech synthesis on unmount");
  });
});
